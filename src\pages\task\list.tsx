import React, { useState, useRef } from 'react';
import { YTHList, YTHLocalization, YTHDialog } from 'yth-ui';
import { ActionType, IYTHColumnProps } from 'yth-ui/es/components/list';
import { Button, message, Space } from 'antd';
import locales from '@/locales';
import dicParams from '@/pages/InspectionPlan/dicParams';
import { DownloadOutlined } from '@ant-design/icons';
import TaskApi from '@/service/taskApi';
import { TaskVo, TaskPageQueryParam, TaskPageResponse, TaskQueryParam } from '@/types/task';
import baseApi from '@/service/baseApi';
import AddDialog from './addDialog';

/**
 * @description 任务管理 任务信息管理
 */
const TaskList: React.FC = () => {
  const ref: React.MutableRefObject<ActionType | undefined> = useRef<ActionType>();
  const aa: ActionType = YTHList.createAction();
  const [modalType, setModalType] = useState<string>(''); // 是否是新增模式
  const [dataObj, setDataObj] = useState<{ [key: string]: React.Key }>({}); // 查看或编辑行数据
  const [editMenuVisiable, setEditMenuVisiable] = useState<boolean>(false); // 标记弹窗是否显示

  const [queryData, setQueryData] = useState<TaskPageQueryParam>(); // 查询参数 用于导出
  const [listDataTotal, setListDataTotal] = useState<number>(0);

  // 导出数据
  const exportByPage: () => Promise<void> = async () => {
    if (listDataTotal === 0) {
      message.warn('无可导出数据！');
      return;
    }
    if (listDataTotal > 10000) {
      message.warn('当前查询结果超过10000条，仅导出前10000条数据');
    }
    const params: TaskPageQueryParam = {
      ...queryData,
      pageSize: 10000,
      currentPage: 1,
    };
    try {
      await TaskApi.exportByPage(params);
    } catch (error) {
      message.error(error);
    }
  };

  const columns: IYTHColumnProps[] = [
    { dataIndex: 'serialNo', title: '序号', width: 80, display: false },
    { dataIndex: 'taskCode', title: '任务编码', query: true, display: true },
    {
      dataIndex: 'taskName',
      title: '任务名称',
      query: true,
      display: true,
      componentProps: {
        placeholder: '名称关键词',
      },
    },
    { dataIndex: 'planName', title: '所属计划', width: 100, query: false },
    { dataIndex: 'inspectionMethodText', title: '巡检方式', width: 120, query: false },
    { dataIndex: 'taskTypeText', title: '任务类型', width: 100, query: false },
    {
      dataIndex: 'inspectionMethod',
      title: '巡检方式',
      width: 0,
      query: true,
      display: true,
      componentName: 'Selector',
      componentProps: {
        p_props: {
          allowClear: true,
          placeholder: '选择巡检方式',
        },
        request: async () => {
          return (await baseApi.getDictionary(dicParams.INSPECTION_METHOD)) ?? [];
        },
      },
    },
    {
      dataIndex: 'taskType',
      title: '任务类型',
      width: 0,
      query: true,
      display: true,
      componentName: 'Selector',
      componentProps: {
        p_props: {
          allowClear: true,
          placeholder: '选择任务类型',
        },
        request: async () => {
          return (await baseApi.getDictionary(dicParams.PLAN_TYPE)) ?? [];
        },
      },
    },
    {
      dataIndex: 'taskStatus',
      title: '任务状态',
      width: 0,
      query: true,
      display: true,
      componentName: 'Selector',
      componentProps: {
        p_props: {
          allowClear: true,
          placeholder: '选择任务状态',
        },
        request: async () => {
          return (await baseApi.getDictionary(dicParams.TASK_STATUS)) ?? [];
        },
      },
    },
    { dataIndex: 'directorUserName', title: '负责人', width: 90, query: false, display: true },
    { dataIndex: 'taskStatusText', title: '任务状态', width: 100, query: false, display: true },
    { dataIndex: 'planStartTime', title: '计划开始时间', width: 150, query: false, display: true },
    { dataIndex: 'planEndTime', title: '计划结束时间', width: 150, query: false, display: true },
    {
      dataIndex: 'actualStartTime',
      title: '实际开始时间',
      width: 150,
      query: false,
      display: true,
    },
    { dataIndex: 'actualEndTime', title: '实际结束时间', width: 150, query: false, display: true },
    {
      dataIndex: 'startDate',
      title: '计划开始时间',
      width: 0,
      queryMode: 'group',
      display: false,
      query: true,
      componentName: 'DatePicker',
      componentProps: {
        placeholder: '请输入',
        precision: `day`,
        formatter: `YYYY-MM-DD`,
      },
    },
  ];

  // 关闭弹窗
  const closeModal: () => void = () => {
    setEditMenuVisiable(false);
    aa.reload({});
  };

  // const confirmDelete: (row: TaskVo) => Promise<void> = async (row) => {
  //   const res: { code?: number; msg?: string } = await TaskApi.deleteById(row.id);
  //   if (res && res.code && res.code === 200) {
  //     message.success('删除数据成功');
  //     aa.reload({});
  //   } else {
  //     message.error('删除数据出错');
  //   }
  // };

  // const deleteTemplateDialog: (row: TaskVo) => Promise<void> = async (row) => {
  //   YTHDialog.show({
  //     type: 'confirm',
  //     content: <p>确认删除此条数据？</p>,
  //     onCancle: () => {},
  //     onConfirm: () => {
  //       confirmDelete(row);
  //     },
  //     p_props: {
  //       cancelText: '取消',
  //       okText: '确定',
  //       title: '删除',
  //     },
  //     m_props: {
  //       title: '删除',
  //     },
  //   });
  // };

  // 获取任务状态的实际值（处理字符串或对象数组格式）
  const getTaskStatusValue: (
    taskStatus:
      | string
      | { code?: string; text?: string; id?: string; value?: string; lable?: string }[]
      | undefined,
  ) => string = (taskStatus) => {
    if (typeof taskStatus === 'string') {
      return taskStatus;
    }
    if (Array.isArray(taskStatus) && taskStatus.length > 0) {
      return taskStatus[0].code || taskStatus[0].value || '';
    }
    return '';
  };

  const handleFilter: (f: TaskQueryParam) => TaskQueryParam = (f: TaskQueryParam) => {
    const filter: TaskQueryParam = f || {};
    if (f.taskName && f.taskName !== '') {
      filter.taskName = f.taskName;
    }
    if (f.inspectionMethod && Array.isArray(f.inspectionMethod) && f.inspectionMethod.length > 0) {
      filter.inspectionMethod = f.inspectionMethod[0].code;
    }
    if (f.taskType && Array.isArray(f.taskType) && f.taskType.length > 0) {
      filter.taskType = f.taskType[0].code;
    }
    if (f.directorUserName && f.directorUserName !== '') {
      filter.directorUserName = f.directorUserName;
    }
    if (f.taskStatus && Array.isArray(f.taskStatus) && f.taskStatus.length > 0) {
      filter.taskStatus = f.taskStatus[0].code;
    }
    if (f.isRemind && Array.isArray(f.isRemind) && f.isRemind.length > 0) {
      filter.isRemind = f.isRemind[0].code;
    }
    if (f.startDate_start && f.startDate_start !== '') {
      filter.startDate = f.startDate_start;
    }
    if (f.startDate_end && f.startDate_end !== '') {
      filter.endDate = f.startDate_end;
    }
    return filter;
  };

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <YTHList
        defaultQuery={{}}
        code="taskList"
        action={aa}
        actionRef={ref}
        showRowSelection={false}
        extraOperation={[
          {
            element: (
              <div>
                <Button
                  type="primary"
                  icon={<DownloadOutlined />}
                  size="small"
                  onClick={exportByPage}
                >
                  {' '}
                  导出{' '}
                </Button>
              </div>
            ),
          },
        ]}
        operation={[
          {
            element: (
              <div>
                <Button
                  size="small"
                  type="primary"
                  onClick={() => {
                    setEditMenuVisiable(true);
                    setModalType('add');
                  }}
                >
                  {' '}
                  新增{' '}
                </Button>
              </div>
            ),
          },
        ]}
        listKey="id"
        request={async (filter, pagination, sort) => {
          try {
            const convertFieldName: (field: string) => string = (field: string) =>
              field.replace(/([a-z])([A-Z])/g, '$1_$2').toLowerCase();

            let descColumns: string = 'create_date';
            let ascColumns: string = '';

            if (sort?.order && sort.field) {
              const convertedField: string = convertFieldName(sort.field);
              if (sort.order === 'desc') {
                descColumns = convertedField;
              } else if (sort.order === 'asc') {
                ascColumns = convertedField;
              }
            }
            const queryParams: TaskPageQueryParam = {
              descs: [descColumns],
              aescs: [ascColumns],
              condition: handleFilter(filter),
              currentPage: pagination.current,
              pageSize: pagination.pageSize,
            };
            setQueryData(queryParams);
            const resData: TaskPageResponse = await TaskApi.queryByPage(queryParams);
            if (resData.code && resData.code === 200) {
              const dataWithSerialNo: (TaskVo & { serialNo: number })[] = resData.data.map(
                (item: TaskVo, index: number) => ({
                  ...item,
                  serialNo: (pagination.current - 1) * pagination.pageSize + index + 1,
                }),
              );
              setListDataTotal(resData.total);
              return {
                data: dataWithSerialNo,
                total: resData.total,
                success: true,
              };
            }
            message.error('请求数据出错，请刷新重试或联系管理员');
            setListDataTotal(0);
            return {
              data: [],
              total: 0,
              success: false,
            };
          } catch {
            message.error('请求数据出错，请刷新重试或联系管理员');
            setListDataTotal(0);
            return {
              data: [],
              total: 0,
              success: false,
            };
          }
        }}
        rowOperationWidth={200}
        rowProps={() => ({})}
        rowOperation={(row) => {
          return [
            {
              element: (
                <div className="task-row-operator">
                  <Space size="middle">
                    <Button
                      size="small"
                      type="link"
                      onClick={() => {
                        setModalType('view');
                        setDataObj(row);
                        setEditMenuVisiable(true);
                      }}
                    >
                      查看
                    </Button>

                    {dicParams.TASK_STATUS_FINISH !== getTaskStatusValue(row.taskStatus) ? (
                      <Button
                        size="small"
                        type="link"
                        onClick={() => {
                          setModalType('edit');
                          setDataObj(row);
                          setEditMenuVisiable(true);
                        }}
                      >
                        编辑
                      </Button>
                    ) : null}
                    {dicParams.TASK_STATUS_WAIT === getTaskStatusValue(row.taskStatus) ? (
                      <Button
                        size="small"
                        type="link"
                        onClick={() => {
                          setModalType('startTask');
                          setDataObj(row);
                          setEditMenuVisiable(true);
                        }}
                      >
                        开始任务
                      </Button>
                    ) : null}
                    {dicParams.TASK_STATUS_DOING === getTaskStatusValue(row.taskStatus) ? (
                      <Button
                        size="small"
                        type="link"
                        onClick={() => {
                          setModalType('endTask');
                          setDataObj(row);
                          setEditMenuVisiable(true);
                        }}
                      >
                        完成任务
                      </Button>
                    ) : null}
                    {/* <Button
                      size="small"
                      type="link"
                      danger
                      onClick={() => {
                        deleteTemplateDialog(row as TaskVo);
                      }}
                    >
                      删除
                    </Button> */}
                  </Space>
                </div>
              ),
            },
          ];
        }}
        columns={columns}
      />

      <AddDialog
        type={modalType}
        closeModal={closeModal}
        dataObj={dataObj}
        visible={editMenuVisiable}
      />
    </div>
  );
};

export default YTHLocalization.withLocal(TaskList, locales['zh-CN'], YTHLocalization.getLanguage());
